// onboarding_screen.dart

import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/widgets/screen_selector.dart';
import 'package:portraitmode/hive/services/local_settings_service.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/onboarding/paragraph_text.dart';
import 'package:portraitmode/onboarding/submission_guidelines.dart';

class OnboardingScreen extends StatefulWidget {
  final bool isLoggedIn;

  const OnboardingScreen({super.key, this.isLoggedIn = false});

  @override
  OnboardingScreenState createState() => OnboardingScreenState();
}

class OnboardingScreenState extends State<OnboardingScreen> {
  static const int _totalPages = 4;
  final ValueNotifier<int> _currentPageNotifier = ValueNotifier<int>(0);
  final PageController _pageController = PageController(initialPage: 0);
  final ScrollController _scrollController = ScrollController();

  late final List<Widget> _pages;
  String? _displayName;

  @override
  void initState() {
    super.initState();

    if (widget.isLoggedIn) {
      _displayName = LocalUserService.displayName;
    }

    // Build pages once during initialization
    _pages = _buildPages();
  }

  @override
  void dispose() {
    _currentPageNotifier.dispose();
    _pageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  List<Widget> _buildPages() {
    return [
      _WelcomePage(context: context),
      _SubmissionGuidelinesPage(
        scrollController: _scrollController,
        displayName: _displayName,
      ),
      _CategoriesPage(context: context),
      _PostPhotosPage(context: context),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Container(
          constraints: const BoxConstraints.expand(),
          child: PageView(
            controller: _pageController,
            onPageChanged: (int pageIndex) {
              if (!mounted) return;
              _currentPageNotifier.value = pageIndex;
            },
            children: _pages,
          ),
        ),
      ),
      bottomNavigationBar: SafeArea(
        child: _BottomNavigationSection(
          currentPageNotifier: _currentPageNotifier,
          totalPages: _totalPages,
          onNext: _handleNextButtonPressed,
          onDone: _handleDoneButtonPressed,
        ),
      ),
    );
  }

  void _handleNextButtonPressed() {
    _pageController.nextPage(
      duration: const Duration(milliseconds: 500),
      curve: Curves.ease,
    );
  }

  Future<void> _handleDoneButtonPressed() async {
    await LocalSettingsService.setDoneOnboarding(true);

    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => const ScreenSelector()),
      );
    }
  }
}

class _WelcomePage extends StatelessWidget {
  final BuildContext context;

  const _WelcomePage({required this.context});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 55.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
            child: _OptimizedImage(
              imagePath:
                  'assets/onboarding/onboarding-illustration-welcome${context.isDarkMode ? '-dark' : ''}.jpg',
            ),
          ),
          const SizedBox(height: 34),
          const Text(
            'Welcome to PortraitMode',
            style: TextStyle(
              fontSize: 20.0,
              height: 1.5,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 14),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ParagraphText(
                text:
                    "PortraitMode is the fastest-growing platform for sharing street photography. Join our global community, build your portfolio, and share your photos.",
                context: context,
              ),
              const SizedBox(height: 14.0),
              ParagraphText(
                text: "It's 100% free. Now & forever.",
                context: context,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _SubmissionGuidelinesPage extends StatelessWidget {
  final ScrollController scrollController;
  final String? displayName;

  const _SubmissionGuidelinesPage({
    required this.scrollController,
    this.displayName,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ListView(
        controller: scrollController,
        padding: const EdgeInsets.only(
          top: 45.0,
          right: 55.0,
          left: 55.0,
          bottom: 20.0,
        ),
        children: [
          const Text(
            'Submission guidelines',
            style: TextStyle(
              fontSize: 20.0,
              height: 1.5,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 14),
          SubmissionGuidelines(artistDisplayName: displayName),
        ],
      ),
    );
  }
}

class _CategoriesPage extends StatelessWidget {
  final BuildContext context;

  const _CategoriesPage({required this.context});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 55.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
            child: _OptimizedImage(
              imagePath:
                  'assets/onboarding/onboarding-illustration-categories${context.isDarkMode ? '-dark' : ''}.jpg',
            ),
          ),
          const SizedBox(height: 34),
          const Text(
            'No hashtags, but categories',
            style: TextStyle(
              fontSize: 20.0,
              height: 1.5,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 14),
          Text(
            "Increase discoverability - Categorize your photos and help us make PortraitMode the best possible resource for street photography.",
            style: TextStyle(
              fontSize: 14,
              height: 1.8,
              color: context.colors.brandColorAlt,
            ),
          ),
        ],
      ),
    );
  }
}

class _PostPhotosPage extends StatelessWidget {
  final BuildContext context;

  const _PostPhotosPage({required this.context});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 55.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
            child: _OptimizedImage(
              imagePath:
                  'assets/onboarding/onboarding-illustration-post-photos${context.isDarkMode ? '-dark' : ''}.jpg',
            ),
          ),
          const SizedBox(height: 34),
          const Text(
            'Post your first 3 photos',
            style: TextStyle(
              fontSize: 20.0,
              height: 1.5,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 14),
          Text(
            'Share your first 3 photos to get listed on PortraitMode and have other photographers discover you and your street photography.',
            style: TextStyle(
              fontSize: 14,
              height: 1.8,
              color: context.colors.brandColorAlt,
            ),
          ),
        ],
      ),
    );
  }
}

class _BottomNavigationSection extends StatelessWidget {
  final ValueNotifier<int> currentPageNotifier;
  final int totalPages;
  final VoidCallback onNext;
  final VoidCallback onDone;

  const _BottomNavigationSection({
    required this.currentPageNotifier,
    required this.totalPages,
    required this.onNext,
    required this.onDone,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 55),
      margin: const EdgeInsets.only(bottom: 20.0),
      height: 60.0,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return ValueListenableBuilder<int>(
            valueListenable: currentPageNotifier,
            builder: (context, currentPageIndex, child) {
              return Stack(
                children: [
                  // Page indicators
                  if (currentPageIndex != totalPages - 1)
                    Positioned.fill(
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: _PageIndicators(
                          currentPageNotifier: currentPageNotifier,
                          totalPages: totalPages,
                        ),
                      ),
                    ),
                  // Action button
                  Positioned(
                    right: 0,
                    top: 0,
                    bottom: 0,
                    child: _ActionButton(
                      currentPageNotifier: currentPageNotifier,
                      totalPages: totalPages,
                      maxWidth: constraints.maxWidth,
                      onNext: onNext,
                      onDone: onDone,
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }
}

class _PageIndicators extends StatelessWidget {
  final ValueNotifier<int> currentPageNotifier;
  final int totalPages;

  const _PageIndicators({
    required this.currentPageNotifier,
    required this.totalPages,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: currentPageNotifier,
      builder: (context, currentPageIndex, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            for (int i = 0; i < totalPages; i++)
              _PageIndicatorDot(
                isActive: i == currentPageIndex,
                context: context,
              ),
          ],
        );
      },
    );
  }
}

class _PageIndicatorDot extends StatelessWidget {
  final bool isActive;
  final BuildContext context;

  const _PageIndicatorDot({required this.isActive, required this.context});

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 350),
      margin: const EdgeInsets.symmetric(horizontal: 5.0),
      height: isActive ? 10.0 : 6.0,
      width: isActive ? 10.0 : 6.0,
      decoration: BoxDecoration(
        color: isActive
            ? context.colors.brandColorAlt
            : (context.isDarkMode
                  ? context.colors.baseColorAlt
                  : context.colors.baseColor),
        borderRadius: const BorderRadius.all(Radius.circular(12)),
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final ValueNotifier<int> currentPageNotifier;
  final int totalPages;
  final double maxWidth;
  final VoidCallback onNext;
  final VoidCallback onDone;

  const _ActionButton({
    required this.currentPageNotifier,
    required this.totalPages,
    required this.maxWidth,
    required this.onNext,
    required this.onDone,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: currentPageNotifier,
      builder: (context, currentPageIndex, child) {
        final isLastPage = currentPageIndex == totalPages - 1;

        return AnimatedContainer(
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeInOut,
          width: isLastPage ? maxWidth : 60.0,
          height: 60.0,
          child: FilledButton(
            onPressed: isLastPage ? onDone : onNext,
            style: FilledButton.styleFrom(
              padding: const EdgeInsets.all(0.0),
              elevation: 0.0,
              backgroundColor: context.isDarkMode
                  ? context.colors.accentColor
                  : context.colors.brandColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(isLastPage ? 30.0 : 30.0),
              ),
            ),
            child: AnimatedCrossFade(
              duration: const Duration(milliseconds: 300),
              firstChild: const Icon(
                Ionicons.arrow_forward_outline,
                size: 20.0,
                color: Colors.white,
              ),
              secondChild: const Text(
                "Let's go!",
                style: TextStyle(color: Colors.white, fontSize: 16.0),
              ),
              crossFadeState: isLastPage
                  ? CrossFadeState.showSecond
                  : CrossFadeState.showFirst,
            ),
          ),
        );
      },
    );
  }
}

class _OptimizedImage extends StatelessWidget {
  final String imagePath;

  const _OptimizedImage({required this.imagePath});

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      imagePath,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) {
          return child;
        }
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: frame != null
              ? child
              : const SizedBox(
                  height: 200,
                  child: Center(child: CircularProgressIndicator()),
                ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return const SizedBox(
          height: 200,
          child: Center(child: Icon(Icons.error_outline, size: 50)),
        );
      },
    );
  }
}
